/**
 * ==================== GoMyHire 对话分析系统 - QA优化系统 ====================
 * @SERVICE 从standalone.html完整迁移的QA优化系统
 * 实现AI集成的问答优化系统，包括标签分析、去重管理、优化流程等功能
 */

import { generateUniqueId, safeExecute } from './utils.js';

// ==================== 标签分类体系 ====================
/**
 * 标签分类体系 - 定义问答标签的分类结构
 * @DATA_STRUCTURE 标签分类体系
 */
export const TAG_CATEGORIES = {
    primary: ['订单问题', '收入问题', '车辆问题', '技术问题', '注册问题', '平台使用', '政策规则', '其他问题'],
    secondary: ['紧急', '常见', '复杂', '简单', '特殊情况'],
    difficulty: [1, 2, 3, 4, 5], // 1=简单, 5=复杂
    frequency: ['高频', '中频', '低频'],
    urgency: ['立即处理', '优先处理', '正常处理', '延后处理']
};

// ==================== 关键词映射表 ====================
/**
 * 关键词映射表 - 关键词到标签的映射关系
 * @DATA_STRUCTURE 关键词映射表
 */
export const KEYWORD_TAG_MAPPING = {
    '订单': ['订单问题', '常见'],
    '取消': ['订单问题', '紧急'],
    '收入': ['收入问题', '常见'],
    '提现': ['收入问题', '紧急'],
    '车辆': ['车辆问题', '常见'],
    '违章': ['车辆问题', '紧急'],
    '技术': ['技术问题', '复杂'],
    'APP': ['技术问题', '常见'],
    '注册': ['注册问题', '简单'],
    '认证': ['注册问题', '复杂'],
    '平台': ['平台使用', '常见'],
    '规则': ['政策规则', '复杂']
};

// ==================== 问答标签分析器类 ====================
/**
 * 问答标签分析器类 - 负责为问答条目自动添加分类标签
 * @CLASS 问答标签分析器
 */
export class QATagAnalyzer {
    constructor() {
        this.tagCategories = TAG_CATEGORIES;
        this.keywordMapping = KEYWORD_TAG_MAPPING;
    }

    /**
     * 分析问答条目并生成标签
     * @SERVICE 问答标签分析方法
     * @param {Object} qaItem - 问答条目
     * @returns {Promise<Object>} 标签分析结果
     */
    async analyzeAndTag(qaItem) {
        try {
            const tags = {
                primary: [],
                secondary: [],
                difficulty: 2, // 默认难度
                frequency: '中频',
                urgency: '正常处理',
                auto: true // 标记为自动生成
            };

            const content = `${qaItem.question || ''} ${qaItem.answer || ''}`.toLowerCase();

            // 基于关键词匹配生成标签
            for (const [keyword, keywordTags] of Object.entries(this.keywordMapping)) {
                if (content.includes(keyword.toLowerCase())) {
                    keywordTags.forEach(tag => {
                        if (this.tagCategories.primary.includes(tag) && !tags.primary.includes(tag)) {
                            tags.primary.push(tag);
                        } else if (this.tagCategories.secondary.includes(tag) && !tags.secondary.includes(tag)) {
                            tags.secondary.push(tag);
                        }
                    });
                }
            }

            // 如果没有匹配到主要标签，设置默认标签
            if (tags.primary.length === 0) {
                tags.primary.push('其他问题');
            }

            // 分析难度等级
            tags.difficulty = this.analyzeDifficulty(content);

            // 分析频率
            tags.frequency = this.analyzeFrequency(qaItem);

            // 分析紧急程度
            tags.urgency = this.analyzeUrgency(content);

            return {
                success: true,
                tags: tags,
                confidence: this.calculateConfidence(tags, content)
            };

        } catch (error) {
            console.error('标签分析失败:', error);
            return {
                success: false,
                error: error.message,
                tags: null
            };
        }
    }

    /**
     * 分析难度等级
     * @UTIL 难度分析工具
     * @param {string} content - 内容文本
     * @returns {number} 难度等级 (1-5)
     */
    analyzeDifficulty(content) {
        let difficulty = 2; // 默认中等难度

        // 复杂关键词增加难度
        const complexKeywords = ['技术', '系统', '算法', '规则', '政策', '法律'];
        const simpleKeywords = ['注册', '登录', '基本', '简单', '常见'];

        complexKeywords.forEach(keyword => {
            if (content.includes(keyword)) difficulty += 0.5;
        });

        simpleKeywords.forEach(keyword => {
            if (content.includes(keyword)) difficulty -= 0.5;
        });

        // 根据内容长度调整难度
        if (content.length > 200) difficulty += 0.5;
        if (content.length < 50) difficulty -= 0.5;

        return Math.max(1, Math.min(5, Math.round(difficulty)));
    }

    /**
     * 分析频率
     * @UTIL 频率分析工具
     * @param {Object} qaItem - 问答条目
     * @returns {string} 频率等级
     */
    analyzeFrequency(qaItem) {
        const frequency = qaItem.frequency || 1;
        
        if (frequency >= 10) return '高频';
        if (frequency >= 5) return '中频';
        return '低频';
    }

    /**
     * 分析紧急程度
     * @UTIL 紧急程度分析工具
     * @param {string} content - 内容文本
     * @returns {string} 紧急程度
     */
    analyzeUrgency(content) {
        const urgentKeywords = ['紧急', '立即', '马上', '急', '重要', '严重'];
        const normalKeywords = ['一般', '普通', '常规'];

        for (const keyword of urgentKeywords) {
            if (content.includes(keyword)) {
                return '立即处理';
            }
        }

        for (const keyword of normalKeywords) {
            if (content.includes(keyword)) {
                return '正常处理';
            }
        }

        return '正常处理';
    }

    /**
     * 计算标签置信度
     * @UTIL 置信度计算工具
     * @param {Object} tags - 标签对象
     * @param {string} content - 内容文本
     * @returns {number} 置信度 (0-1)
     */
    calculateConfidence(tags, content) {
        let confidence = 0.5; // 基础置信度

        // 根据匹配的关键词数量调整置信度
        let matchedKeywords = 0;
        for (const keyword of Object.keys(this.keywordMapping)) {
            if (content.includes(keyword.toLowerCase())) {
                matchedKeywords++;
            }
        }

        confidence += Math.min(0.4, matchedKeywords * 0.1);

        // 根据标签数量调整置信度
        if (tags.primary.length > 0) confidence += 0.1;
        if (tags.secondary.length > 0) confidence += 0.1;

        return Math.min(1, confidence);
    }

    /**
     * 批量标签分析
     * @SERVICE 批量标签分析方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 批量分析结果
     */
    async batchAnalyze(qaItems, progressCallback) {
        const results = [];
        const total = qaItems.length;

        for (let i = 0; i < qaItems.length; i++) {
            const item = qaItems[i];
            
            try {
                const result = await this.analyzeAndTag(item);
                results.push({
                    index: i,
                    item: item,
                    result: result
                });

                // 更新进度
                if (progressCallback) {
                    progressCallback({
                        step: '标签分析',
                        current: i + 1,
                        total: total,
                        percentage: Math.round(((i + 1) / total) * 100)
                    });
                }

            } catch (error) {
                console.error(`标签分析失败 (索引 ${i}):`, error);
                results.push({
                    index: i,
                    item: item,
                    result: { success: false, error: error.message }
                });
            }
        }

        return {
            success: true,
            results: results,
            summary: {
                total: total,
                successful: results.filter(r => r.result.success).length,
                failed: results.filter(r => !r.result.success).length
            }
        };
    }
}

// ==================== 问答去重优化管理器类 ====================
/**
 * 问答去重优化管理器类 - 负责批量去重和质量优化
 * @CLASS 问答去重优化管理器
 */
export class QADeduplicationManager {
    constructor() {
        this.batchSize = 100;
        this.similarityThreshold = 0.85;
        this.processedCount = 0;
        this.duplicateGroups = [];
        this.qualityScores = new Map();
    }

    /**
     * 处理批量去重
     * @SERVICE 批量去重处理方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 去重处理结果
     */
    async processBatch(qaItems, progressCallback) {
        try {
            console.log(`[去重优化] 开始处理 ${qaItems.length} 个问答条目`);

            // 第一步：相似度分析和分组
            const similarGroups = await this.findSimilarItems(qaItems, progressCallback);

            // 第二步：质量评估和选择
            const optimizedItems = await this.selectBestItems(similarGroups, progressCallback);

            // 第三步：生成结果报告
            const result = {
                success: true,
                original: {
                    count: qaItems.length,
                    items: qaItems
                },
                optimized: {
                    count: optimizedItems.length,
                    items: optimizedItems
                },
                duplicateGroups: this.duplicateGroups,
                statistics: {
                    duplicatesFound: qaItems.length - optimizedItems.length,
                    reductionRate: ((qaItems.length - optimizedItems.length) / qaItems.length * 100).toFixed(2)
                }
            };

            console.log(`[去重优化] 完成处理，从 ${qaItems.length} 条优化到 ${optimizedItems.length} 条`);
            return result;

        } catch (error) {
            console.error('[去重优化] 处理失败:', error);
            return {
                success: false,
                error: error.message,
                original: { count: qaItems.length, items: qaItems },
                optimized: { count: 0, items: [] }
            };
        }
    }

    /**
     * 查找相似项目
     * @SERVICE 相似项目查找方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Array>} 相似项目分组
     */
    async findSimilarItems(qaItems, progressCallback) {
        const groups = [];
        const processed = new Set();

        for (let i = 0; i < qaItems.length; i++) {
            if (processed.has(i)) continue;

            const currentItem = qaItems[i];
            const similarItems = [{ index: i, item: currentItem }];

            // 查找相似项目
            for (let j = i + 1; j < qaItems.length; j++) {
                if (processed.has(j)) continue;

                const similarity = this.calculateSimilarity(currentItem, qaItems[j]);
                if (similarity >= this.similarityThreshold) {
                    similarItems.push({ index: j, item: qaItems[j] });
                    processed.add(j);
                }
            }

            groups.push(similarItems);
            processed.add(i);

            // 更新进度
            if (progressCallback) {
                progressCallback({
                    step: '相似度分析',
                    current: i + 1,
                    total: qaItems.length,
                    percentage: Math.round(((i + 1) / qaItems.length) * 50) // 占总进度50%
                });
            }
        }

        return groups;
    }

    /**
     * 计算相似度
     * @UTIL 相似度计算工具
     * @param {Object} item1 - 问答条目1
     * @param {Object} item2 - 问答条目2
     * @returns {number} 相似度 (0-1)
     */
    calculateSimilarity(item1, item2) {
        const text1 = `${item1.question || ''} ${item1.answer || ''}`.toLowerCase();
        const text2 = `${item2.question || ''} ${item2.answer || ''}`.toLowerCase();

        // 简单的字符串相似度计算
        return this.jaccardSimilarity(text1, text2);
    }

    /**
     * Jaccard相似度计算
     * @UTIL Jaccard相似度计算工具
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} Jaccard相似度
     */
    jaccardSimilarity(str1, str2) {
        const set1 = new Set(str1.split(''));
        const set2 = new Set(str2.split(''));
        
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        return intersection.size / union.size;
    }

    /**
     * 选择最佳项目
     * @SERVICE 最佳项目选择方法
     * @param {Array} groups - 相似项目分组
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Array>} 优化后的项目列表
     */
    async selectBestItems(groups, progressCallback) {
        const selectedItems = [];

        for (let i = 0; i < groups.length; i++) {
            const group = groups[i];
            
            if (group.length === 1) {
                // 单个项目直接选择
                selectedItems.push(group[0].item);
            } else {
                // 多个相似项目，选择质量最高的
                const bestItem = this.selectBestFromGroup(group);
                selectedItems.push(bestItem);
                
                // 记录重复组
                this.duplicateGroups.push({
                    selected: bestItem,
                    duplicates: group.filter(item => item.item !== bestItem).map(item => item.item)
                });
            }

            // 更新进度
            if (progressCallback) {
                progressCallback({
                    step: '质量评估',
                    current: i + 1,
                    total: groups.length,
                    percentage: 50 + Math.round(((i + 1) / groups.length) * 50) // 占总进度50%
                });
            }
        }

        return selectedItems;
    }

    /**
     * 从组中选择最佳项目
     * @UTIL 最佳项目选择工具
     * @param {Array} group - 相似项目组
     * @returns {Object} 最佳项目
     */
    selectBestFromGroup(group) {
        let bestItem = group[0].item;
        let bestScore = this.calculateQualityScore(bestItem);

        for (let i = 1; i < group.length; i++) {
            const item = group[i].item;
            const score = this.calculateQualityScore(item);
            
            if (score > bestScore) {
                bestScore = score;
                bestItem = item;
            }
        }

        return bestItem;
    }

    /**
     * 计算质量分数
     * @UTIL 质量分数计算工具
     * @param {Object} item - 问答条目
     * @returns {number} 质量分数
     */
    calculateQualityScore(item) {
        let score = 0;

        // 内容长度评分
        const questionLength = (item.question || '').length;
        const answerLength = (item.answer || '').length;
        score += Math.min(20, questionLength / 5); // 问题长度
        score += Math.min(30, answerLength / 10); // 答案长度

        // 频率评分
        score += (item.frequency || 1) * 5;

        // 标签完整性评分
        if (item.tags && item.tags.length > 0) {
            score += item.tags.length * 2;
        }

        // 是否有步骤说明
        if (item.steps && item.steps.length > 0) {
            score += 10;
        }

        return score;
    }

    /**
     * 获取去重统计
     * @SERVICE 去重统计获取方法
     * @returns {Object} 去重统计信息
     */
    getDeduplicationStats() {
        return {
            processedCount: this.processedCount,
            duplicateGroupsCount: this.duplicateGroups.length,
            totalDuplicatesRemoved: this.duplicateGroups.reduce((sum, group) => sum + group.duplicates.length, 0)
        };
    }

    /**
     * 清理缓存
     * @SERVICE 缓存清理方法
     */
    clearCache() {
        this.duplicateGroups = [];
        this.qualityScores.clear();
        this.processedCount = 0;
    }
}

// ==================== 问答优化管理器类 ====================
/**
 * 问答优化管理器类 - 统一管理标签化和去重优化流程
 * @CLASS 问答优化管理器
 */
export class QAOptimizationManager {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.tagAnalyzer = new QATagAnalyzer();
        this.deduplicationManager = new QADeduplicationManager();
        this.batchSize = 100;
        this.isProcessing = false;
        this.processedCount = 0;
        this.optimizationHistory = [];
        this.maxHistorySize = 20;

        // 加载历史记录
        this.loadOptimizationHistory();
    }

    /**
     * 检查是否需要优化
     * @SERVICE 优化需求检查方法
     * @param {number} qaDatasetLength - 问答数据集长度
     * @returns {boolean} 是否需要优化
     */
    shouldOptimize(qaDatasetLength) {
        return qaDatasetLength >= this.batchSize;
    }

    /**
     * 执行完整优化流程
     * @SERVICE 完整优化流程执行方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Object} options - 优化选项
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeQADataset(qaItems, options = {}) {
        if (this.isProcessing) {
            throw new Error('优化流程正在进行中，请稍后再试');
        }

        this.isProcessing = true;
        const startTime = Date.now();

        try {
            console.log(`[问答优化] 开始优化 ${qaItems.length} 个问答条目`);

            const result = {
                success: false,
                startTime: startTime,
                endTime: null,
                duration: 0,
                original: {
                    count: qaItems.length,
                    items: qaItems
                },
                optimized: {
                    count: 0,
                    items: []
                },
                tagging: null,
                deduplication: null,
                statistics: {},
                error: null
            };

            // 第一步：标签分析
            if (options.progressCallback) {
                options.progressCallback({
                    stage: 'tagging',
                    step: '开始标签分析',
                    current: 0,
                    total: qaItems.length,
                    percentage: 0
                });
            }

            const taggingResult = await this.performTagging(qaItems, options.progressCallback);
            result.tagging = taggingResult;

            if (!taggingResult.success) {
                throw new Error('标签分析失败: ' + taggingResult.error);
            }

            // 第二步：去重优化
            if (options.progressCallback) {
                options.progressCallback({
                    stage: 'deduplication',
                    step: '开始去重优化',
                    current: 0,
                    total: qaItems.length,
                    percentage: 50
                });
            }

            const deduplicationResult = await this.performDeduplication(qaItems, options.progressCallback);
            result.deduplication = deduplicationResult;

            if (!deduplicationResult.success) {
                throw new Error('去重优化失败: ' + deduplicationResult.error);
            }

            // 合并结果
            result.optimized = deduplicationResult.optimized;
            result.success = true;
            result.endTime = Date.now();
            result.duration = result.endTime - result.startTime;

            // 计算统计信息
            result.statistics = {
                originalCount: result.original.count,
                optimizedCount: result.optimized.count,
                removedCount: result.original.count - result.optimized.count,
                reductionRate: ((result.original.count - result.optimized.count) / result.original.count * 100).toFixed(2),
                processingTime: result.duration,
                taggingSuccess: taggingResult.summary.successful,
                taggingFailed: taggingResult.summary.failed,
                duplicatesRemoved: deduplicationResult.statistics.duplicatesFound
            };

            // 保存优化历史
            this.saveOptimizationHistory(result);

            // 触发优化完成事件
            if (this.eventBus) {
                this.eventBus.emit('qa.optimization.completed', result);
            }

            console.log(`[问答优化] 优化完成，耗时 ${result.duration}ms`);
            return result;

        } catch (error) {
            console.error('[问答优化] 优化失败:', error);

            const result = {
                success: false,
                startTime: startTime,
                endTime: Date.now(),
                duration: Date.now() - startTime,
                error: error.message,
                original: { count: qaItems.length, items: qaItems },
                optimized: { count: 0, items: [] }
            };

            // 触发优化失败事件
            if (this.eventBus) {
                this.eventBus.emit('qa.optimization.failed', result);
            }

            return result;
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 执行标签分析
     * @SERVICE 标签分析执行方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 标签分析结果
     */
    async performTagging(qaItems, progressCallback) {
        try {
            const result = await this.tagAnalyzer.batchAnalyze(qaItems, (progress) => {
                if (progressCallback) {
                    progressCallback({
                        stage: 'tagging',
                        step: progress.step,
                        current: progress.current,
                        total: progress.total,
                        percentage: Math.round(progress.percentage / 2), // 标签分析占总进度50%
                        details: progress
                    });
                }
            });

            // 应用标签到原始数据
            result.results.forEach(item => {
                if (item.result.success) {
                    Object.assign(item.item, { tags: item.result.tags });
                }
            });

            return result;
        } catch (error) {
            console.error('[标签分析] 执行失败:', error);
            return {
                success: false,
                error: error.message,
                results: [],
                summary: { total: qaItems.length, successful: 0, failed: qaItems.length }
            };
        }
    }

    /**
     * 执行去重优化
     * @SERVICE 去重优化执行方法
     * @param {Array} qaItems - 问答条目列表
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 去重优化结果
     */
    async performDeduplication(qaItems, progressCallback) {
        try {
            const result = await this.deduplicationManager.processBatch(qaItems, (progress) => {
                if (progressCallback) {
                    progressCallback({
                        stage: 'deduplication',
                        step: progress.step,
                        current: progress.current,
                        total: progress.total,
                        percentage: 50 + Math.round(progress.percentage / 2), // 去重占总进度50%
                        details: progress
                    });
                }
            });

            return result;
        } catch (error) {
            console.error('[去重优化] 执行失败:', error);
            return {
                success: false,
                error: error.message,
                original: { count: qaItems.length, items: qaItems },
                optimized: { count: 0, items: [] }
            };
        }
    }

    /**
     * 保存优化历史
     * @SERVICE 优化历史保存方法
     * @param {Object} result - 优化结果
     */
    saveOptimizationHistory(result) {
        const historyItem = {
            id: generateUniqueId('opt'),
            timestamp: result.startTime,
            duration: result.duration,
            originalCount: result.original.count,
            optimizedCount: result.optimized.count,
            reductionRate: result.statistics.reductionRate,
            success: result.success,
            error: result.error || null
        };

        this.optimizationHistory.push(historyItem);

        // 只保留最近的记录
        if (this.optimizationHistory.length > this.maxHistorySize) {
            this.optimizationHistory = this.optimizationHistory.slice(-this.maxHistorySize);
        }

        // 保存到localStorage
        safeExecute(() => {
            localStorage.setItem('qa_optimization_history', JSON.stringify(this.optimizationHistory));
        });
    }

    /**
     * 获取优化历史
     * @SERVICE 优化历史获取方法
     * @returns {Array} 优化历史列表
     */
    getOptimizationHistory() {
        return [...this.optimizationHistory];
    }

    /**
     * 获取优化统计
     * @SERVICE 优化统计获取方法
     * @returns {Object} 优化统计信息
     */
    getOptimizationStats() {
        const history = this.optimizationHistory.filter(item => item.success);

        if (history.length === 0) {
            return {
                totalOptimizations: 0,
                totalItemsProcessed: 0,
                totalItemsRemoved: 0,
                averageReductionRate: 0,
                averageDuration: 0
            };
        }

        const totalProcessed = history.reduce((sum, item) => sum + item.originalCount, 0);
        const totalRemoved = history.reduce((sum, item) => sum + (item.originalCount - item.optimizedCount), 0);
        const totalDuration = history.reduce((sum, item) => sum + item.duration, 0);

        return {
            totalOptimizations: history.length,
            totalItemsProcessed: totalProcessed,
            totalItemsRemoved: totalRemoved,
            averageReductionRate: (totalRemoved / totalProcessed * 100).toFixed(2),
            averageDuration: Math.round(totalDuration / history.length)
        };
    }

    /**
     * 清理优化历史
     * @SERVICE 优化历史清理方法
     */
    clearOptimizationHistory() {
        this.optimizationHistory = [];
        safeExecute(() => {
            localStorage.removeItem('qa_optimization_history');
        });
    }

    /**
     * 加载优化历史
     * @SERVICE 优化历史加载方法
     */
    loadOptimizationHistory() {
        safeExecute(() => {
            const saved = localStorage.getItem('qa_optimization_history');
            if (saved) {
                this.optimizationHistory = JSON.parse(saved);
            }
        }, () => {
            console.warn('加载优化历史失败');
            this.optimizationHistory = [];
        });
    }

    /**
     * 获取处理状态
     * @SERVICE 处理状态获取方法
     * @returns {Object} 处理状态信息
     */
    getProcessingStatus() {
        return {
            isProcessing: this.isProcessing,
            processedCount: this.processedCount,
            batchSize: this.batchSize
        };
    }

    /**
     * 销毁优化管理器
     * @LIFECYCLE 优化管理器销毁方法
     */
    destroy() {
        this.isProcessing = false;
        this.tagAnalyzer = null;
        this.deduplicationManager = null;
        this.optimizationHistory = [];
        console.log('🗑️ QAOptimizationManager 已销毁');
    }
}

// ==================== 优化工具函数 ====================

/**
 * 初始化问答优化管理器
 * @SERVICE 优化管理器初始化函数
 * @param {Object} eventBus - 事件总线
 * @returns {QAOptimizationManager} 优化管理器实例
 */
export function initializeQAOptimization(eventBus = null) {
    const manager = new QAOptimizationManager(eventBus);
    console.log('✓ 问答优化管理器初始化完成');
    return manager;
}

/**
 * 显示优化进度对话框
 * @SERVICE 优化进度显示函数
 * @returns {HTMLElement} 进度对话框元素
 */
export function showOptimizationProgressDialog() {
    const dialog = document.createElement('div');
    dialog.id = 'optimization-progress-dialog';
    dialog.className = 'modal-overlay';
    dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    dialog.innerHTML = `
        <div class="modal-content" style="width: 500px; max-width: 90vw;">
            <div class="modal-header">
                <h3><i class="fas fa-cogs"></i> 问答优化进行中</h3>
            </div>
            <div class="modal-body">
                <div class="optimization-progress">
                    <div class="progress-info">
                        <div id="optimization-stage">准备中...</div>
                        <div id="optimization-current-step">初始化优化流程</div>
                    </div>
                    <div class="progress-bar">
                        <div id="optimization-progress-fill" class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text">
                        <span id="optimization-progress-percentage">0%</span>
                        <span id="optimization-status">准备中</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);
    return dialog;
}

/**
 * 更新优化进度
 * @SERVICE 优化进度更新函数
 * @param {Object} progress - 进度信息
 */
export function updateOptimizationProgress(progress) {
    const progressFill = document.getElementById('optimization-progress-fill');
    const progressPercentage = document.getElementById('optimization-progress-percentage');
    const currentStep = document.getElementById('optimization-current-step');
    const stage = document.getElementById('optimization-stage');
    const status = document.getElementById('optimization-status');

    if (progressFill) {
        progressFill.style.width = `${progress.percentage}%`;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${progress.percentage}%`;
    }

    if (currentStep) {
        currentStep.textContent = progress.step || '处理中...';
    }

    if (stage) {
        const stageNames = {
            tagging: '标签分析',
            deduplication: '去重优化'
        };
        stage.textContent = stageNames[progress.stage] || progress.stage;
    }

    if (status) {
        status.textContent = `${progress.current || 0}/${progress.total || 0}`;
    }
}

/**
 * 隐藏优化进度对话框
 * @SERVICE 优化进度隐藏函数
 */
export function hideOptimizationProgressDialog() {
    const dialog = document.getElementById('optimization-progress-dialog');
    if (dialog && dialog.parentNode) {
        dialog.parentNode.removeChild(dialog);
    }
}
