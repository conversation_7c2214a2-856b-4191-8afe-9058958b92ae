/**
 * ==================== 数据处理系统模块集成测试 ====================
 * @TEST 数据处理相关模块的集成测试脚本
 * 验证存储管理器、数据处理器、性能监控器等模块的功能完整性
 */

// 测试结果统计
const testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: []
};

// 日志函数
function log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${type}] ${message}`);
}

// 断言函数
function assert(condition, message) {
    testResults.total++;
    if (condition) {
        testResults.passed++;
        log(`✅ ${message}`, 'PASS');
        return true;
    } else {
        testResults.failed++;
        testResults.errors.push(message);
        log(`❌ ${message}`, 'FAIL');
        return false;
    }
}

// 异步测试包装器
async function runTest(testName, testFunction) {
    log(`🧪 开始测试: ${testName}`, 'TEST');
    try {
        await testFunction();
        log(`✅ 测试完成: ${testName}`, 'TEST');
    } catch (error) {
        log(`❌ 测试失败: ${testName} - ${error.message}`, 'ERROR');
        testResults.failed++;
        testResults.errors.push(`${testName}: ${error.message}`);
    }
}

// 存储管理器测试
async function testStorageManager() {
    // 动态导入模块
    const { StorageManager } = await import('../src/storage-manager.js');
    
    const storage = new StorageManager();
    
    // 测试基础功能
    const testData = { name: '测试数据', value: 123, timestamp: Date.now() };
    
    // 保存测试
    const saveResult = storage.save('test-key', testData);
    assert(saveResult === true, '数据保存功能正常');
    
    // 加载测试
    const loadedData = storage.load('test-key');
    assert(JSON.stringify(loadedData) === JSON.stringify(testData), '数据加载功能正常');
    
    // 存在性检查测试
    assert(storage.exists('test-key') === true, '键存在性检查正常');
    assert(storage.exists('non-existent-key') === false, '不存在键检查正常');
    
    // 批量操作测试
    const batchData = {
        'batch-1': { value: 1 },
        'batch-2': { value: 2 },
        'batch-3': { value: 3 }
    };
    
    const batchResult = storage.saveBatch(batchData);
    assert(batchResult.success === 3 && batchResult.failed === 0, '批量保存功能正常');
    
    const batchLoadResult = storage.loadBatch(['batch-1', 'batch-2', 'batch-3']);
    assert(Object.keys(batchLoadResult).length === 3, '批量加载功能正常');
    
    // 存储信息测试
    const storageInfo = storage.getStorageInfo();
    assert(typeof storageInfo.totalKeys === 'number', '存储信息获取正常');
    assert(storageInfo.totalKeys >= 4, '存储键数量统计正确');
    
    // 清理测试数据
    storage.remove('test-key');
    ['batch-1', 'batch-2', 'batch-3'].forEach(key => storage.remove(key));
}

// 增强存储管理器测试
async function testEnhancedStorageManager() {
    const { EnhancedStorageManager } = await import('../src/storage-manager.js');
    
    const enhancedStorage = new EnhancedStorageManager();
    
    // 测试智能存储
    const testData = {
        content: 'x'.repeat(2000), // 大于压缩阈值的数据
        metadata: { type: 'test', priority: 'high' }
    };
    
    const storeResult = await enhancedStorage.store('enhanced-test', testData, {
        type: 'test-data',
        priority: 'high'
    });
    assert(storeResult === true, '智能存储功能正常');
    
    // 测试智能检索
    const retrievedData = await enhancedStorage.retrieve('enhanced-test');
    assert(retrievedData && retrievedData.content === testData.content, '智能检索功能正常');
    
    // 测试性能指标
    const metrics = enhancedStorage.getPerformanceMetrics();
    assert(typeof metrics.writes === 'number' && metrics.writes >= 1, '性能指标记录正常');
    assert(typeof metrics.cacheHitRate === 'string', '缓存命中率计算正常');
    
    // 清理测试数据
    enhancedStorage.destroy();
}

// 数据处理器测试
async function testDataProcessor() {
    const { DataProcessor } = await import('../src/data-processor.js');
    
    const processor = new DataProcessor();
    
    // 测试统计计算
    const stats = processor.calculateStatistics();
    assert(typeof stats === 'object', '统计计算功能正常');
    assert(typeof stats.totalQAItems === 'number', '问答项目统计正常');
    
    // 测试问答项目添加
    const qaData = {
        question: '测试问题',
        answer: '测试答案',
        difficulty: 'easy',
        tags: ['测试', '示例'],
        isCommon: true
    };
    
    const addResult = processor.addQAItem(qaData);
    assert(addResult === true, '问答项目添加功能正常');
    
    // 测试问答数据集获取
    const dataset = processor.getQADataset();
    assert(Array.isArray(dataset), '问答数据集获取正常');
    assert(dataset.length >= 1, '问答数据集包含添加的项目');
    
    // 测试搜索功能
    const searchResults = processor.searchQAItems('测试');
    assert(Array.isArray(searchResults), '搜索功能返回数组');
    assert(searchResults.length >= 1, '搜索功能找到相关结果');
    
    // 测试CSV导出
    const csvContent = processor.exportQADatasetCSV();
    assert(typeof csvContent === 'string', 'CSV导出功能正常');
    assert(csvContent.includes('测试问题'), 'CSV内容包含测试数据');
    
    // 测试JSON导出
    const jsonContent = processor.exportQADatasetJSON();
    assert(typeof jsonContent === 'string', 'JSON导出功能正常');
    
    const parsedJson = JSON.parse(jsonContent);
    assert(Array.isArray(parsedJson.data), 'JSON导出格式正确');
    
    // 测试数据清理
    const cleanupResult = processor.cleanupData({ removeEmpty: true, removeDuplicates: true });
    assert(typeof cleanupResult.removed === 'number', '数据清理功能正常');
}

// 应用数据管理器测试
async function testAppDataManager() {
    const { AppDataManager } = await import('../src/data-processor.js');
    
    const appDataManager = new AppDataManager();
    appDataManager.initialize();
    
    // 测试数据设置和获取
    const testData = { test: 'value', timestamp: Date.now() };
    appDataManager.setData('testKey', testData);
    
    const retrievedData = appDataManager.getData('testKey');
    assert(JSON.stringify(retrievedData) === JSON.stringify(testData), '数据设置和获取功能正常');
    
    // 测试问答项目添加
    const qaItem = {
        question: '应用管理器测试问题',
        answer: '应用管理器测试答案',
        tags: ['应用管理器', '测试']
    };
    
    const addResult = appDataManager.addQAItem(qaItem);
    assert(addResult === true, '应用数据管理器问答项目添加正常');
    
    // 测试统计信息
    const statistics = appDataManager.getStatistics();
    assert(typeof statistics === 'object', '统计信息获取正常');
    assert(typeof statistics.totalQAItems === 'number', '问答项目统计正常');
    
    // 测试CSV导出
    const csvExport = appDataManager.exportQADatasetCSV();
    assert(typeof csvExport === 'string', '应用数据管理器CSV导出正常');
}

// 性能监控器测试
async function testPerformanceMonitor() {
    const { PerformanceTimer, PerformanceMonitor } = await import('../src/performance-monitor.js');
    
    // 测试性能计时器
    const timer = new PerformanceTimer('测试计时器');
    timer.start();
    
    // 模拟一些工作
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const lapTime = timer.lap('中间点');
    assert(typeof lapTime === 'number' && lapTime > 0, '分段计时功能正常');
    
    const duration = timer.end();
    assert(typeof duration === 'number' && duration > 0, '计时器功能正常');
    
    // 测试性能监控器
    const monitor = new PerformanceMonitor();
    
    // 启动监控
    monitor.startMonitoring(500); // 500ms间隔
    
    // 等待收集一些数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 获取性能报告
    const report = monitor.getPerformanceReport();
    assert(typeof report === 'object', '性能报告获取正常');
    assert(typeof report.current === 'object', '当前性能指标正常');
    assert(report.isMonitoring === true, '监控状态正确');
    
    // 检查性能警告
    const warnings = monitor.checkPerformanceWarnings();
    assert(Array.isArray(warnings), '性能警告检查正常');
    
    // 停止监控
    monitor.stopMonitoring();
    assert(monitor.isMonitoring === false, '监控停止功能正常');
    
    // 清理
    monitor.destroy();
}

// UI性能监控器测试
async function testUIPerformanceMonitor() {
    const { UIPerformanceMonitor } = await import('../src/performance-monitor.js');
    
    const uiMonitor = new UIPerformanceMonitor();
    
    // 启动监控
    uiMonitor.startMonitoring(500);
    
    // 等待收集数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 获取报告
    const report = uiMonitor.getPerformanceReport();
    assert(typeof report === 'object', 'UI性能报告获取正常');
    assert(typeof report.current.fps === 'number', 'FPS指标正常');
    
    // 记录渲染时间
    const startTime = performance.now();
    await new Promise(resolve => setTimeout(resolve, 50));
    const endTime = performance.now();
    
    uiMonitor.recordRenderTime(startTime, endTime);
    assert(uiMonitor.metrics.renderTime > 0, '渲染时间记录正常');
    
    // 清理
    uiMonitor.destroy();
}

// 图表性能监控器测试
async function testChartPerformanceMonitor() {
    const { ChartPerformanceMonitor } = await import('../src/performance-monitor.js');
    
    const chartMonitor = new ChartPerformanceMonitor();
    
    // 启动监控
    chartMonitor.startMonitoring(500);
    
    // 记录图表渲染
    chartMonitor.recordRender('test-chart', 150, 'immediate');
    chartMonitor.recordRender('test-chart', 200, 'deferred');
    
    // 获取性能统计
    const stats = chartMonitor.getPerformanceStats();
    assert(typeof stats === 'object', '图表性能统计获取正常');
    assert(stats.totalCharts >= 1, '图表数量统计正常');
    
    // 获取特定图表性能
    const chartPerf = chartMonitor.getChartPerformance('test-chart');
    assert(chartPerf !== null, '特定图表性能获取正常');
    assert(chartPerf.totalRenders === 2, '图表渲染次数统计正确');
    
    // 清理
    chartMonitor.destroy();
}

// 模块集成测试
async function testModuleIntegration() {
    log('🔄 开始模块集成测试...', 'TEST');
    
    // 测试模块间的协作
    const { StorageManager } = await import('../src/storage-manager.js');
    const { DataProcessor } = await import('../src/data-processor.js');
    const { PerformanceTimer } = await import('../src/performance-monitor.js');
    
    const storage = new StorageManager();
    const processor = new DataProcessor();
    const timer = new PerformanceTimer('集成测试');
    
    timer.start();
    
    // 添加一些测试数据
    const testQAItems = [
        { question: '集成测试问题1', answer: '集成测试答案1', tags: ['集成', '测试'] },
        { question: '集成测试问题2', answer: '集成测试答案2', tags: ['集成', '测试'] },
        { question: '集成测试问题3', answer: '集成测试答案3', tags: ['集成', '测试'] }
    ];
    
    // 批量添加问答项目
    const batchResult = processor.addQAItems(testQAItems);
    assert(batchResult.addedCount === 3, '批量添加问答项目成功');
    
    timer.lap('数据添加完成');
    
    // 导出数据
    const csvData = processor.exportQADatasetCSV();
    assert(typeof csvData === 'string' && csvData.length > 0, '数据导出成功');
    
    // 保存导出的数据到存储
    const saveResult = storage.save('exported-qa-data', csvData);
    assert(saveResult === true, '导出数据保存成功');
    
    timer.lap('数据导出和保存完成');
    
    // 验证保存的数据
    const savedData = storage.load('exported-qa-data');
    assert(savedData === csvData, '保存的数据验证成功');
    
    const duration = timer.end();
    assert(duration > 0, '集成测试计时正常');
    
    // 清理测试数据
    storage.remove('exported-qa-data');
    
    log('✅ 模块集成测试完成', 'TEST');
}

// 主测试函数
async function runAllTests() {
    log('🚀 开始数据处理系统集成测试...', 'START');
    
    // 运行所有测试
    await runTest('存储管理器测试', testStorageManager);
    await runTest('增强存储管理器测试', testEnhancedStorageManager);
    await runTest('数据处理器测试', testDataProcessor);
    await runTest('应用数据管理器测试', testAppDataManager);
    await runTest('性能监控器测试', testPerformanceMonitor);
    await runTest('UI性能监控器测试', testUIPerformanceMonitor);
    await runTest('图表性能监控器测试', testChartPerformanceMonitor);
    await runTest('模块集成测试', testModuleIntegration);
    
    // 输出测试结果
    log('📊 测试结果统计:', 'RESULT');
    log(`   总测试数: ${testResults.total}`, 'RESULT');
    log(`   通过测试: ${testResults.passed}`, 'RESULT');
    log(`   失败测试: ${testResults.failed}`, 'RESULT');
    log(`   成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`, 'RESULT');
    
    if (testResults.failed > 0) {
        log('❌ 失败的测试:', 'ERROR');
        testResults.errors.forEach(error => log(`   - ${error}`, 'ERROR'));
    }
    
    log('🏁 数据处理系统集成测试完成!', 'END');
    
    return testResults;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runAllTests, testResults };
} else {
    // 在浏览器环境中，将函数暴露到全局
    window.runDataProcessingTests = runAllTests;
}

// 自动运行测试（如果直接执行此文件）
if (typeof window === 'undefined') {
    runAllTests().then(results => {
        process.exit(results.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}
