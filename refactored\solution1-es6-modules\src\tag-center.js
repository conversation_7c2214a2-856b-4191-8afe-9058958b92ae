/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Tag system constants: TAG_CENTER_* prefix if added.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 标签中心系统 ====================
 * @SERVICE 从standalone.html完整迁移的标签中心系统
 * 实现标签管理、分类、防重复检查等功能，包括标签中心管理器、重复检查器等
 */

import { generateUniqueId, safeExecute } from './utils.js';

// ==================== 标签中心管理器类 ====================
/**
 * 标签中心管理器类 - 负责标签的统一管理和分类
 * @SERVICE 标签中心管理器类
 */
export class TagCenter {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.tags = new Map(); // 标签存储
        this.categories = new Map(); // 标签分类
        this.taggedItems = new Map(); // 被标记的项目
        this.isInitialized = true;

        // 预定义标签类别
        this.initializeTagCategories();

        console.log('🏷️ TagCenter 初始化完成');
    }

    /**
     * 初始化标签类别
     * @INIT 标签类别初始化方法
     */
    initializeTagCategories() {
        // 依赖标签类别
        this.categories.set('DEPENDENCY', {
            name: '依赖标签',
            description: '标记模块间的依赖关系',
            color: '#007bff',
            tags: ['@DEPENDENCY']
        });

        // 初始化标签类别
        this.categories.set('INIT', {
            name: '初始化标签',
            description: '标记初始化相关的代码',
            color: '#28a745',
            tags: ['@INIT']
        });

        // 声明标签类别
        this.categories.set('DECLARATION', {
            name: '声明标签',
            description: '标记变量、函数、类的声明',
            color: '#17a2b8',
            tags: ['@DECLARATION']
        });

        // 生命周期标签类别
        this.categories.set('LIFECYCLE', {
            name: '生命周期标签',
            description: '标记组件生命周期相关的代码',
            color: '#ffc107',
            tags: ['@LIFECYCLE']
        });

        // 引用标签类别
        this.categories.set('REFERENCE', {
            name: '引用标签',
            description: '标记引用关系',
            color: '#6f42c1',
            tags: ['@REFERENCE']
        });

        // 服务标签类别
        this.categories.set('SERVICE', {
            name: '服务标签',
            description: '标记服务函数和方法',
            color: '#fd7e14',
            tags: ['@SERVICE']
        });

        // 管理器标签类别
        this.categories.set('MANAGER', {
            name: '管理器标签',
            description: '标记管理器类',
            color: '#e83e8c',
            tags: ['@MANAGER']
        });

        // 工厂标签类别
        this.categories.set('FACTORY', {
            name: '工厂标签',
            description: '标记工厂函数',
            color: '#20c997',
            tags: ['@FACTORY']
        });

        // 工具标签类别
        this.categories.set('UTIL', {
            name: '工具标签',
            description: '标记工具函数',
            color: '#6c757d',
            tags: ['@UTIL']
        });

        // 组件标签类别
        this.categories.set('COMPONENT', {
            name: '组件标签',
            description: '标记UI组件',
            color: '#dc3545',
            tags: ['@COMPONENT']
        });

        console.log('✓ 标签类别初始化完成');
    }

    /**
     * 注册标签
     * @SERVICE 标签注册方法
     * @param {string} tag - 标签名称
     * @param {any} item - 被标记的项目
     * @param {Object} metadata - 元数据
     * @returns {TagCenter} 返回自身以支持链式调用
     */
    registerTag(tag, item, metadata = {}) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }

        const tagInfo = {
            tag, item,
            metadata: {
                type: metadata.type || 'unknown',
                description: metadata.description || '',
                location: metadata.location || '',
                module: metadata.module || 'unknown',
                createdAt: Date.now(),
                ...metadata
            }
        };

        // 存储标签
        if (!this.tags.has(tag)) {
            this.tags.set(tag, []);
        }
        this.tags.get(tag).push(tagInfo);

        // 存储被标记的项目
        const itemId = this.generateItemId(item);
        if (!this.taggedItems.has(itemId)) {
            this.taggedItems.set(itemId, []);
        }
        this.taggedItems.get(itemId).push(tag);

        console.log(`🏷️ 标签已注册: ${tag} -> ${metadata.type || 'unknown'}`);
        return this;
    }

    /**
     * 根据标签查找项目
     * @SERVICE 标签查找方法
     * @param {string} tag - 标签名称
     * @returns {Array} 匹配的项目列表
     */
    findByTag(tag) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }
        return this.tags.get(tag) || [];
    }

    /**
     * 获取标签统计
     * @SERVICE 标签统计获取方法
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            totalTags: this.tags.size,
            totalItems: 0,
            totalCategories: this.categories.size,
            byCategory: {}
        };

        // 统计项目数量
        for (const items of this.tags.values()) {
            stats.totalItems += items.length;
        }

        // 按类别统计
        for (const [categoryName, categoryInfo] of this.categories) {
            stats.byCategory[categoryName] = {
                name: categoryInfo.name,
                count: 0,
                tags: []
            };

            for (const tag of categoryInfo.tags) {
                const tagItems = this.tags.get(tag) || [];
                stats.byCategory[categoryName].count += tagItems.length;
                if (tagItems.length > 0) {
                    stats.byCategory[categoryName].tags.push({
                        tag,
                        count: tagItems.length
                    });
                }
            }
        }

        return stats;
    }

    /**
     * 生成项目ID
     * @UTIL 项目ID生成工具
     * @param {any} item - 项目
     * @returns {string} 项目ID
     */
    generateItemId(item) {
        if (typeof item === 'string') {
            return item;
        } else if (typeof item === 'function') {
            return item.name || 'anonymous_function';
        } else if (typeof item === 'object' && item !== null) {
            return item.constructor.name + '_' + (item.id || generateUniqueId('item'));
        } else {
            return 'unknown_' + generateUniqueId('item');
        }
    }

    /**
     * 获取项目的所有标签
     * @SERVICE 项目标签获取方法
     * @param {any} item - 项目
     * @returns {Array} 标签列表
     */
    getItemTags(item) {
        const itemId = this.generateItemId(item);
        return this.taggedItems.get(itemId) || [];
    }

    /**
     * 移除标签
     * @SERVICE 标签移除方法
     * @param {string} tag - 标签名称
     * @param {any} item - 被标记的项目（可选）
     * @returns {boolean} 移除是否成功
     */
    removeTag(tag, item = null) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }

        if (item === null) {
            // 移除整个标签
            const removed = this.tags.delete(tag);
            if (removed) {
                console.log(`🗑️ 标签已移除: ${tag}`);
            }
            return removed;
        } else {
            // 移除特定项目的标签
            const tagItems = this.tags.get(tag);
            if (tagItems) {
                const itemId = this.generateItemId(item);
                const index = tagItems.findIndex(tagInfo => 
                    this.generateItemId(tagInfo.item) === itemId
                );
                if (index !== -1) {
                    tagItems.splice(index, 1);
                    if (tagItems.length === 0) {
                        this.tags.delete(tag);
                    }
                    
                    // 从被标记项目中移除标签
                    const itemTags = this.taggedItems.get(itemId);
                    if (itemTags) {
                        const tagIndex = itemTags.indexOf(tag);
                        if (tagIndex !== -1) {
                            itemTags.splice(tagIndex, 1);
                            if (itemTags.length === 0) {
                                this.taggedItems.delete(itemId);
                            }
                        }
                    }
                    
                    console.log(`🗑️ 项目标签已移除: ${tag} -> ${itemId}`);
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 清空所有标签
     * @SERVICE 标签清空方法
     */
    clearAll() {
        this.tags.clear();
        this.taggedItems.clear();
        console.log('🧹 所有标签已清空');
    }

    /**
     * 获取所有标签
     * @SERVICE 所有标签获取方法
     * @returns {Array} 标签列表
     */
    getAllTags() {
        return Array.from(this.tags.keys());
    }

    /**
     * 获取标签详情
     * @SERVICE 标签详情获取方法
     * @param {string} tag - 标签名称
     * @returns {Object|null} 标签详情
     */
    getTagDetails(tag) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }

        const items = this.tags.get(tag);
        if (!items) return null;

        return {
            tag,
            itemCount: items.length,
            items: items.map(tagInfo => ({
                item: tagInfo.item,
                metadata: tagInfo.metadata
            })),
            category: this.getTagCategory(tag)
        };
    }

    /**
     * 获取标签所属类别
     * @UTIL 标签类别获取工具
     * @param {string} tag - 标签名称
     * @returns {string|null} 类别名称
     */
    getTagCategory(tag) {
        for (const [categoryName, categoryInfo] of this.categories) {
            if (categoryInfo.tags.includes(tag)) {
                return categoryName;
            }
        }
        return null;
    }

    /**
     * 搜索标签
     * @SERVICE 标签搜索方法
     * @param {string} query - 搜索查询
     * @returns {Array} 匹配的标签列表
     */
    searchTags(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        for (const [tag, items] of this.tags) {
            if (tag.toLowerCase().includes(lowerQuery)) {
                results.push({
                    tag,
                    itemCount: items.length,
                    relevance: this.calculateRelevance(tag, query)
                });
            }
        }

        // 按相关性排序
        return results.sort((a, b) => b.relevance - a.relevance);
    }

    /**
     * 计算相关性
     * @UTIL 相关性计算工具
     * @param {string} tag - 标签名称
     * @param {string} query - 查询字符串
     * @returns {number} 相关性分数
     */
    calculateRelevance(tag, query) {
        const lowerTag = tag.toLowerCase();
        const lowerQuery = query.toLowerCase();

        if (lowerTag === lowerQuery) return 100;
        if (lowerTag.startsWith(lowerQuery)) return 80;
        if (lowerTag.includes(lowerQuery)) return 60;
        return 0;
    }

    /**
     * 导出标签数据
     * @SERVICE 标签数据导出方法
     * @returns {Object} 导出的数据
     */
    exportData() {
        const data = {
            tags: {},
            categories: Object.fromEntries(this.categories),
            exportedAt: new Date().toISOString()
        };

        for (const [tag, items] of this.tags) {
            data.tags[tag] = items.map(tagInfo => ({
                metadata: tagInfo.metadata,
                itemId: this.generateItemId(tagInfo.item)
            }));
        }

        return data;
    }

    /**
     * 导入标签数据
     * @SERVICE 标签数据导入方法
     * @param {Object} data - 导入的数据
     * @returns {boolean} 导入是否成功
     */
    importData(data) {
        try {
            if (data.categories) {
                this.categories = new Map(Object.entries(data.categories));
            }

            if (data.tags) {
                this.tags.clear();
                this.taggedItems.clear();

                for (const [tag, items] of Object.entries(data.tags)) {
                    this.tags.set(tag, items.map(itemData => ({
                        tag,
                        item: itemData.itemId, // 使用ID作为占位符
                        metadata: itemData.metadata
                    })));
                }
            }

            console.log('✓ 标签数据导入成功');
            return true;
        } catch (error) {
            console.error('标签数据导入失败:', error);
            return false;
        }
    }

    /**
     * 销毁标签中心
     * @LIFECYCLE 标签中心销毁方法
     */
    destroy() {
        this.clearAll();
        this.categories.clear();
        this.isInitialized = false;
        console.log('🗑️ TagCenter 已销毁');
    }
}

// ==================== 防重复开发检查器类 ====================
/**
 * 防重复开发检查器类 - 检测和防止重复开发
 * @SERVICE 防重复开发检查器类
 */
export class DuplicationChecker {
    constructor(eventBus = null, globalRegistry = null) {
        this.eventBus = eventBus;
        this.globalRegistry = globalRegistry;
        this.functionRegistry = new Map(); // 函数注册表
        this.classRegistry = new Map(); // 类注册表
        this.duplicatePatterns = new Map(); // 重复模式
        this.similarityThreshold = 0.8; // 相似度阈值

        console.log('🔍 DuplicationChecker 初始化完成');
    }

    /**
     * 注册函数
     * @SERVICE 函数注册方法
     * @param {string} name - 函数名称
     * @param {Function} func - 函数对象
     * @param {Object} metadata - 元数据
     * @returns {Object} 检查结果
     */
    registerFunction(name, func, metadata = {}) {
        const signature = this.generateFunctionSignature(func);
        const functionInfo = {
            name,
            signature,
            metadata: {
                module: metadata.module || 'unknown',
                location: metadata.location || '',
                description: metadata.description || '',
                createdAt: Date.now(),
                ...metadata
            }
        };

        // 检查是否存在相似函数
        const similarFunctions = this.findSimilarFunctions(functionInfo);

        if (similarFunctions.length > 0) {
            console.warn(`⚠️ 发现相似函数: ${name}`, similarFunctions);
            this.recordDuplication('function', name, similarFunctions);
        }

        // 注册函数
        this.functionRegistry.set(name, functionInfo);

        return {
            registered: true,
            duplicates: similarFunctions,
            hasDuplicates: similarFunctions.length > 0
        };
    }

    /**
     * 注册类
     * @SERVICE 类注册方法
     * @param {string} name - 类名称
     * @param {Function} classConstructor - 类构造函数
     * @param {Object} metadata - 元数据
     * @returns {Object} 检查结果
     */
    registerClass(name, classConstructor, metadata = {}) {
        const signature = this.generateClassSignature(classConstructor);
        const classInfo = {
            name,
            signature,
            metadata: {
                module: metadata.module || 'unknown',
                location: metadata.location || '',
                description: metadata.description || '',
                createdAt: Date.now(),
                ...metadata
            }
        };

        // 检查是否存在相似类
        const similarClasses = this.findSimilarClasses(classInfo);

        if (similarClasses.length > 0) {
            console.warn(`⚠️ 发现相似类: ${name}`, similarClasses);
            this.recordDuplication('class', name, similarClasses);
        }

        // 注册类
        this.classRegistry.set(name, classInfo);

        return {
            registered: true,
            duplicates: similarClasses,
            hasDuplicates: similarClasses.length > 0
        };
    }

    /**
     * 生成函数签名
     * @UTIL 函数签名生成工具
     * @param {Function} func - 函数对象
     * @returns {string} 函数签名
     */
    generateFunctionSignature(func) {
        const funcStr = func.toString();

        // 提取参数列表
        const paramMatch = funcStr.match(/\(([^)]*)\)/);
        const params = paramMatch ? paramMatch[1].split(',').map(p => p.trim()) : [];

        // 提取函数体关键特征
        const bodyFeatures = this.extractCodeFeatures(funcStr);

        return {
            paramCount: params.length,
            params: params,
            bodyLength: funcStr.length,
            features: bodyFeatures
        };
    }

    /**
     * 生成类签名
     * @UTIL 类签名生成工具
     * @param {Function} classConstructor - 类构造函数
     * @returns {string} 类签名
     */
    generateClassSignature(classConstructor) {
        const classStr = classConstructor.toString();

        // 提取方法名
        const methodMatches = classStr.match(/(\w+)\s*\([^)]*\)\s*{/g) || [];
        const methods = methodMatches.map(match => {
            const methodMatch = match.match(/(\w+)\s*\(/);
            return methodMatch ? methodMatch[1] : '';
        }).filter(Boolean);

        // 提取类特征
        const features = this.extractCodeFeatures(classStr);

        return {
            methodCount: methods.length,
            methods: methods,
            bodyLength: classStr.length,
            features: features
        };
    }

    /**
     * 提取代码特征
     * @UTIL 代码特征提取工具
     * @param {string} code - 代码字符串
     * @returns {Object} 代码特征
     */
    extractCodeFeatures(code) {
        return {
            hasAsync: code.includes('async'),
            hasAwait: code.includes('await'),
            hasPromise: code.includes('Promise'),
            hasCallback: code.includes('callback'),
            hasLoop: /for\s*\(|while\s*\(|forEach/.test(code),
            hasCondition: /if\s*\(|switch\s*\(/.test(code),
            hasTryCatch: code.includes('try') && code.includes('catch'),
            hasReturn: code.includes('return'),
            keywordCount: (code.match(/\b(function|class|const|let|var|if|for|while|return)\b/g) || []).length
        };
    }

    /**
     * 查找相似函数
     * @SERVICE 相似函数查找方法
     * @param {Object} functionInfo - 函数信息
     * @returns {Array} 相似函数列表
     */
    findSimilarFunctions(functionInfo) {
        const similar = [];

        for (const [name, existingFunc] of this.functionRegistry) {
            const similarity = this.calculateSimilarity(functionInfo.signature, existingFunc.signature);
            if (similarity >= this.similarityThreshold) {
                similar.push({
                    name,
                    similarity,
                    metadata: existingFunc.metadata
                });
            }
        }

        return similar.sort((a, b) => b.similarity - a.similarity);
    }

    /**
     * 查找相似类
     * @SERVICE 相似类查找方法
     * @param {Object} classInfo - 类信息
     * @returns {Array} 相似类列表
     */
    findSimilarClasses(classInfo) {
        const similar = [];

        for (const [name, existingClass] of this.classRegistry) {
            const similarity = this.calculateSimilarity(classInfo.signature, existingClass.signature);
            if (similarity >= this.similarityThreshold) {
                similar.push({
                    name,
                    similarity,
                    metadata: existingClass.metadata
                });
            }
        }

        return similar.sort((a, b) => b.similarity - a.similarity);
    }

    /**
     * 计算相似度
     * @UTIL 相似度计算工具
     * @param {Object} sig1 - 签名1
     * @param {Object} sig2 - 签名2
     * @returns {number} 相似度 (0-1)
     */
    calculateSimilarity(sig1, sig2) {
        let score = 0;

        // 参数数量相似度
        const paramSimilarity = sig1.paramCount === sig2.paramCount ? 1 :
            Math.max(0, 1 - Math.abs(sig1.paramCount - sig2.paramCount) / Math.max(sig1.paramCount, sig2.paramCount));
        score += paramSimilarity * 0.3;

        // 代码长度相似度
        const lengthSimilarity = Math.max(0, 1 - Math.abs(sig1.bodyLength - sig2.bodyLength) / Math.max(sig1.bodyLength, sig2.bodyLength));
        score += lengthSimilarity * 0.2;

        // 特征相似度
        const featureSimilarity = this.calculateFeatureSimilarity(sig1.features, sig2.features);
        score += featureSimilarity * 0.5;

        return score;
    }

    /**
     * 计算特征相似度
     * @UTIL 特征相似度计算工具
     * @param {Object} features1 - 特征1
     * @param {Object} features2 - 特征2
     * @returns {number} 特征相似度
     */
    calculateFeatureSimilarity(features1, features2) {
        const booleanFeatures = ['hasAsync', 'hasAwait', 'hasPromise', 'hasCallback', 'hasLoop', 'hasCondition', 'hasTryCatch', 'hasReturn'];
        let matches = 0;

        for (const feature of booleanFeatures) {
            if (features1[feature] === features2[feature]) {
                matches++;
            }
        }

        // 关键词数量相似度
        const keywordSimilarity = Math.max(0, 1 - Math.abs(features1.keywordCount - features2.keywordCount) / Math.max(features1.keywordCount, features2.keywordCount));

        return (matches / booleanFeatures.length + keywordSimilarity) / 2;
    }

    /**
     * 记录重复
     * @SERVICE 重复记录方法
     * @param {string} type - 重复类型
     * @param {string} name - 名称
     * @param {Array} similar - 相似项列表
     */
    recordDuplication(type, name, similar) {
        const duplication = {
            type, name, similar,
            timestamp: Date.now(),
            severity: similar.length > 2 ? 'high' : similar.length > 1 ? 'medium' : 'low'
        };

        this.duplicatePatterns.set(`${type}_${name}`, duplication);

        // 触发重复检测事件
        if (this.eventBus) {
            this.eventBus.emit('duplication.detected', duplication);
        }
    }

    /**
     * 获取重复报告
     * @SERVICE 重复报告获取方法
     * @returns {Object} 重复报告
     */
    getDuplicationReport() {
        const report = {
            totalDuplications: this.duplicatePatterns.size,
            byType: {},
            bySeverity: { high: 0, medium: 0, low: 0 },
            details: []
        };

        for (const duplication of this.duplicatePatterns.values()) {
            report.byType[duplication.type] = (report.byType[duplication.type] || 0) + 1;
            report.bySeverity[duplication.severity]++;
            report.details.push(duplication);
        }

        return report;
    }

    /**
     * 检查代码块
     * @SERVICE 代码块检查方法
     * @param {string} code - 代码字符串
     * @param {Object} metadata - 元数据
     * @returns {Object} 检查结果
     */
    checkCodeBlock(code, metadata = {}) {
        const features = this.extractCodeFeatures(code);
        const signature = {
            bodyLength: code.length,
            features: features
        };

        // 查找相似的代码块
        const similarBlocks = [];

        // 与已注册的函数比较
        for (const [name, funcInfo] of this.functionRegistry) {
            const similarity = this.calculateFeatureSimilarity(features, funcInfo.signature.features);
            if (similarity >= this.similarityThreshold) {
                similarBlocks.push({
                    type: 'function',
                    name,
                    similarity,
                    metadata: funcInfo.metadata
                });
            }
        }

        // 与已注册的类比较
        for (const [name, classInfo] of this.classRegistry) {
            const similarity = this.calculateFeatureSimilarity(features, classInfo.signature.features);
            if (similarity >= this.similarityThreshold) {
                similarBlocks.push({
                    type: 'class',
                    name,
                    similarity,
                    metadata: classInfo.metadata
                });
            }
        }

        return {
            hasDuplicates: similarBlocks.length > 0,
            duplicates: similarBlocks.sort((a, b) => b.similarity - a.similarity),
            features: features
        };
    }

    /**
     * 设置相似度阈值
     * @SERVICE 相似度阈值设置方法
     * @param {number} threshold - 阈值 (0-1)
     */
    setSimilarityThreshold(threshold) {
        if (threshold >= 0 && threshold <= 1) {
            this.similarityThreshold = threshold;
            console.log(`🔧 相似度阈值已设置为: ${threshold}`);
        } else {
            console.warn('⚠️ 相似度阈值必须在0-1之间');
        }
    }

    /**
     * 清空注册表
     * @SERVICE 注册表清空方法
     */
    clearRegistry() {
        this.functionRegistry.clear();
        this.classRegistry.clear();
        this.duplicatePatterns.clear();
        console.log('🧹 重复检查器注册表已清空');
    }

    /**
     * 获取统计信息
     * @SERVICE 统计信息获取方法
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalFunctions: this.functionRegistry.size,
            totalClasses: this.classRegistry.size,
            totalDuplications: this.duplicatePatterns.size,
            similarityThreshold: this.similarityThreshold,
            duplicatesByType: this.getDuplicationReport().byType,
            duplicatesBySeverity: this.getDuplicationReport().bySeverity
        };
    }

    /**
     * 销毁检查器
     * @LIFECYCLE 检查器销毁方法
     */
    destroy() {
        this.clearRegistry();
        this.eventBus = null;
        this.globalRegistry = null;
        console.log('🗑️ DuplicationChecker 已销毁');
    }
}

// ==================== 工具函数 ====================

/**
 * 初始化标签中心系统
 * @SERVICE 标签中心系统初始化函数
 * @param {Object} eventBus - 事件总线
 * @returns {Object} 初始化结果
 */
export function initializeTagCenterSystem(eventBus = null) {
    const tagCenter = new TagCenter(eventBus);
    const duplicationChecker = new DuplicationChecker(eventBus);

    // 预注册一些常用标签
    registerCommonTags(tagCenter);

    console.log('✓ 标签中心系统初始化完成');

    return {
        tagCenter,
        duplicationChecker,
        success: true
    };
}

/**
 * 注册常用标签
 * @SERVICE 常用标签注册函数
 * @param {TagCenter} tagCenter - 标签中心实例
 */
function registerCommonTags(tagCenter) {
    // 注册系统级标签
    const systemTags = [
        { tag: '@SYSTEM', description: '系统级组件' },
        { tag: '@CORE', description: '核心功能' },
        { tag: '@API', description: 'API接口' },
        { tag: '@UI', description: '用户界面' },
        { tag: '@DATA', description: '数据处理' },
        { tag: '@STORAGE', description: '存储相关' },
        { tag: '@NETWORK', description: '网络请求' },
        { tag: '@VALIDATION', description: '数据验证' },
        { tag: '@ERROR_HANDLING', description: '错误处理' },
        { tag: '@PERFORMANCE', description: '性能优化' }
    ];

    systemTags.forEach(({ tag, description }) => {
        tagCenter.registerTag(tag, tag, {
            type: 'system',
            description,
            module: 'tag-center',
            location: 'tag-center.js'
        });
    });

    console.log('✓ 常用标签注册完成');
}

/**
 * 创建标签装饰器
 * @FACTORY 标签装饰器工厂函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @returns {Function} 装饰器函数
 */
export function createTagDecorator(tagCenter) {
    return function tagDecorator(tag, metadata = {}) {
        return function(target, propertyKey, descriptor) {
            // 注册标签
            tagCenter.registerTag(tag, target, {
                ...metadata,
                type: typeof target === 'function' ? 'function' : 'class',
                propertyKey,
                location: `${target.name || 'anonymous'}.${propertyKey || 'constructor'}`
            });

            return descriptor;
        };
    };
}

/**
 * 批量标签注册
 * @SERVICE 批量标签注册函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @param {Array} tagDefinitions - 标签定义列表
 * @returns {Object} 注册结果
 */
export function batchRegisterTags(tagCenter, tagDefinitions) {
    const results = {
        successful: 0,
        failed: 0,
        errors: []
    };

    tagDefinitions.forEach((definition, index) => {
        try {
            const { tag, item, metadata } = definition;
            tagCenter.registerTag(tag, item, metadata);
            results.successful++;
        } catch (error) {
            results.failed++;
            results.errors.push({
                index,
                definition,
                error: error.message
            });
            console.error(`标签注册失败 (索引 ${index}):`, error);
        }
    });

    console.log(`📊 批量标签注册完成: 成功 ${results.successful}, 失败 ${results.failed}`);
    return results;
}

/**
 * 生成标签报告
 * @SERVICE 标签报告生成函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @param {DuplicationChecker} duplicationChecker - 重复检查器实例
 * @returns {Object} 标签报告
 */
export function generateTagReport(tagCenter, duplicationChecker) {
    const tagStats = tagCenter.getStats();
    const duplicationStats = duplicationChecker.getStats();
    const duplicationReport = duplicationChecker.getDuplicationReport();

    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            totalTags: tagStats.totalTags,
            totalItems: tagStats.totalItems,
            totalCategories: tagStats.totalCategories,
            totalFunctions: duplicationStats.totalFunctions,
            totalClasses: duplicationStats.totalClasses,
            totalDuplications: duplicationStats.totalDuplications
        },
        tagsByCategory: tagStats.byCategory,
        duplications: {
            byType: duplicationReport.byType,
            bySeverity: duplicationReport.bySeverity,
            details: duplicationReport.details.slice(0, 10) // 只显示前10个
        },
        recommendations: generateTagRecommendations(tagStats, duplicationStats)
    };

    return report;
}

/**
 * 生成标签建议
 * @UTIL 标签建议生成工具
 * @param {Object} tagStats - 标签统计
 * @param {Object} duplicationStats - 重复统计
 * @returns {Array} 建议列表
 */
function generateTagRecommendations(tagStats, duplicationStats) {
    const recommendations = [];

    // 检查标签覆盖率
    if (tagStats.totalItems < 50) {
        recommendations.push({
            type: 'coverage',
            priority: 'medium',
            title: '增加标签覆盖率',
            description: '当前标记的项目较少，建议为更多代码添加标签以提高可维护性'
        });
    }

    // 检查重复情况
    if (duplicationStats.totalDuplications > 0) {
        const severity = duplicationStats.totalDuplications > 5 ? 'high' : 'medium';
        recommendations.push({
            type: 'duplication',
            priority: severity,
            title: '处理代码重复',
            description: `发现 ${duplicationStats.totalDuplications} 处代码重复，建议进行重构以减少冗余`
        });
    }

    // 检查标签分类平衡
    const categoryCount = tagStats.totalCategories;
    if (categoryCount < 5) {
        recommendations.push({
            type: 'categorization',
            priority: 'low',
            title: '扩展标签分类',
            description: '当前标签分类较少，建议添加更多分类以更好地组织代码'
        });
    }

    return recommendations;
}

/**
 * 导出标签中心数据
 * @SERVICE 标签中心数据导出函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @param {DuplicationChecker} duplicationChecker - 重复检查器实例
 * @returns {Object} 导出数据
 */
export function exportTagCenterData(tagCenter, duplicationChecker) {
    return {
        tagCenter: tagCenter.exportData(),
        duplicationChecker: {
            stats: duplicationChecker.getStats(),
            report: duplicationChecker.getDuplicationReport()
        },
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
    };
}

/**
 * 导入标签中心数据
 * @SERVICE 标签中心数据导入函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @param {Object} data - 导入数据
 * @returns {boolean} 导入是否成功
 */
export function importTagCenterData(tagCenter, data) {
    try {
        if (data.tagCenter) {
            return tagCenter.importData(data.tagCenter);
        }
        return false;
    } catch (error) {
        console.error('标签中心数据导入失败:', error);
        return false;
    }
}

/**
 * 搜索标签和项目
 * @SERVICE 标签和项目搜索函数
 * @param {TagCenter} tagCenter - 标签中心实例
 * @param {string} query - 搜索查询
 * @param {Object} options - 搜索选项
 * @returns {Object} 搜索结果
 */
export function searchTagsAndItems(tagCenter, query, options = {}) {
    const {
        includeItems = true,
        includeTags = true,
        maxResults = 20
    } = options;

    const results = {
        tags: [],
        items: [],
        total: 0
    };

    if (includeTags) {
        results.tags = tagCenter.searchTags(query).slice(0, maxResults);
    }

    if (includeItems) {
        // 搜索被标记的项目
        const allTags = tagCenter.getAllTags();
        const lowerQuery = query.toLowerCase();

        for (const tag of allTags) {
            const tagDetails = tagCenter.getTagDetails(tag);
            if (tagDetails) {
                const matchingItems = tagDetails.items.filter(item => {
                    const itemStr = JSON.stringify(item).toLowerCase();
                    return itemStr.includes(lowerQuery);
                });

                results.items.push(...matchingItems.slice(0, maxResults - results.items.length));

                if (results.items.length >= maxResults) break;
            }
        }
    }

    results.total = results.tags.length + results.items.length;
    return results;
}

/**
 * 验证标签格式
 * @UTIL 标签格式验证工具
 * @param {string} tag - 标签名称
 * @returns {Object} 验证结果
 */
export function validateTagFormat(tag) {
    const result = {
        valid: true,
        errors: [],
        warnings: []
    };

    // 检查是否以@开头
    if (!tag.startsWith('@')) {
        result.warnings.push('标签建议以@开头');
    }

    // 检查长度
    if (tag.length < 2) {
        result.valid = false;
        result.errors.push('标签长度不能少于2个字符');
    }

    if (tag.length > 50) {
        result.valid = false;
        result.errors.push('标签长度不能超过50个字符');
    }

    // 检查字符
    if (!/^@?[A-Z_][A-Z0-9_]*$/i.test(tag)) {
        result.valid = false;
        result.errors.push('标签只能包含字母、数字和下划线，且必须以字母或下划线开头');
    }

    // 检查是否为保留字
    const reservedTags = ['@SYSTEM', '@CORE', '@GLOBAL'];
    if (reservedTags.includes(tag.toUpperCase())) {
        result.warnings.push('使用了系统保留标签');
    }

    return result;
}
