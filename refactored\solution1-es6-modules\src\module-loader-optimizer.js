/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Loader optimizer constants: LOADER_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 模块加载优化器 ====================
 * @SERVICE 模块加载顺序优化和依赖关系管理
 * 实现智能模块加载、依赖解析、性能优化等功能
 */

import { generateUniqueId, safeExecute } from './utils.js';
import { getPerformanceMonitor } from './performance-monitor.js';

// ==================== 模块加载优化器类 ====================
/**
 * 模块加载优化器类 - 负责优化模块加载顺序和依赖关系
 * @COMPONENT 模块加载优化器
 */
export class ModuleLoaderOptimizer {
    constructor() {
        this.loadedModules = new Set();
        this.loadingModules = new Map();
        this.moduleCache = new Map();
        this.dependencyGraph = new Map();
        this.loadOrder = [];
        this.performanceMonitor = null;
        this.loadingStats = {
            totalModules: 0,
            loadedModules: 0,
            failedModules: 0,
            totalLoadTime: 0,
            averageLoadTime: 0
        };

        this.initialize();
        console.log('⚡ ModuleLoaderOptimizer 初始化完成');
    }

    /**
     * 初始化优化器
     * @INIT 优化器初始化方法
     */
    initialize() {
        this.performanceMonitor = getPerformanceMonitor();
        this.buildDependencyGraph();
        this.calculateOptimalLoadOrder();
    }

    /**
     * 构建依赖图
     * @SERVICE 依赖图构建方法
     */
    buildDependencyGraph() {
        // 定义模块依赖关系
        const dependencies = {
            // 基础模块
            'utils.js': [],
            'constants.js': [],
            
            // 核心基础设施
            'service-container.js': ['utils.js'],
            'event-bus.js': ['utils.js'],
            'dependency-manager.js': ['utils.js'],
            
            // 数据处理系统
            'storage-manager.js': ['utils.js', 'constants.js'],
            'data-processor.js': ['utils.js', 'storage-manager.js'],
            'performance-monitor.js': ['utils.js'],
            
            // 原有核心模块
            'storage.js': ['constants.js', 'storage-manager.js'],
            'parser.js': ['constants.js', 'utils.js'],
            'charts.js': ['constants.js', 'utils.js'],
            'drag-upload.js': ['utils.js'],
            
            // UI组件系统
            'notification.js': ['utils.js'],
            'modal.js': ['utils.js'],
            'tabs.js': ['utils.js'],
            'progress.js': ['utils.js'],
            
            // 高级功能系统
            'enhanced-upload.js': ['utils.js', 'storage-manager.js', 'progress.js'],
            'qa-optimization.js': ['utils.js', 'data-processor.js'],
            'report-generator.js': ['utils.js', 'data-processor.js', 'progress.js'],
            'tag-center.js': ['utils.js'],
            
            // UI和主应用
            'ui.js': ['notification.js', 'modal.js', 'tabs.js', 'progress.js'],
            'main.js': [
                'constants.js', 'utils.js', 'service-container.js', 'event-bus.js',
                'storage.js', 'parser.js', 'charts.js', 'drag-upload.js', 'ui.js',
                'notification.js', 'modal.js', 'tabs.js', 'progress.js',
                'enhanced-upload.js', 'qa-optimization.js', 'report-generator.js'
            ]
        };

        // 构建依赖图
        for (const [module, deps] of Object.entries(dependencies)) {
            this.dependencyGraph.set(module, new Set(deps));
        }

        console.log(`📊 依赖图构建完成，包含 ${this.dependencyGraph.size} 个模块`);
    }

    /**
     * 计算最优加载顺序
     * @SERVICE 最优加载顺序计算方法
     */
    calculateOptimalLoadOrder() {
        const visited = new Set();
        const visiting = new Set();
        const order = [];

        // 拓扑排序算法
        const visit = (module) => {
            if (visiting.has(module)) {
                throw new Error(`检测到循环依赖: ${module}`);
            }
            
            if (visited.has(module)) {
                return;
            }

            visiting.add(module);
            
            const dependencies = this.dependencyGraph.get(module) || new Set();
            for (const dep of dependencies) {
                visit(dep);
            }
            
            visiting.delete(module);
            visited.add(module);
            order.push(module);
        };

        // 访问所有模块
        for (const module of this.dependencyGraph.keys()) {
            if (!visited.has(module)) {
                visit(module);
            }
        }

        this.loadOrder = order;
        console.log(`🔄 最优加载顺序计算完成: ${order.join(' -> ')}`);
    }

    /**
     * 优化模块加载
     * @SERVICE 模块加载优化方法
     * @param {Array} modules - 要加载的模块列表
     * @returns {Promise<Object>} 加载结果
     */
    async optimizeModuleLoading(modules = []) {
        const startTime = performance.now();
        
        try {
            console.log('🚀 开始优化模块加载...');

            // 如果没有指定模块，使用最优加载顺序
            const modulesToLoad = modules.length > 0 ? modules : this.loadOrder;
            
            // 分析模块依赖
            const loadPlan = this.createLoadPlan(modulesToLoad);
            
            // 执行分批加载
            const results = await this.executeBatchLoading(loadPlan);
            
            // 更新统计信息
            const endTime = performance.now();
            this.updateLoadingStats(results, endTime - startTime);
            
            console.log('✅ 模块加载优化完成');
            return {
                success: true,
                results: results,
                stats: this.loadingStats,
                loadTime: endTime - startTime
            };

        } catch (error) {
            console.error('❌ 模块加载优化失败:', error);
            return {
                success: false,
                error: error.message,
                stats: this.loadingStats
            };
        }
    }

    /**
     * 创建加载计划
     * @SERVICE 加载计划创建方法
     * @param {Array} modules - 模块列表
     * @returns {Array} 加载计划
     */
    createLoadPlan(modules) {
        const plan = [];
        const processed = new Set();
        
        // 按依赖层级分组
        const levels = new Map();
        
        const calculateLevel = (module, level = 0) => {
            if (processed.has(module)) {
                return levels.get(module) || 0;
            }
            
            processed.add(module);
            const dependencies = this.dependencyGraph.get(module) || new Set();
            
            let maxDepLevel = level;
            for (const dep of dependencies) {
                if (modules.includes(dep)) {
                    maxDepLevel = Math.max(maxDepLevel, calculateLevel(dep, level) + 1);
                }
            }
            
            levels.set(module, maxDepLevel);
            return maxDepLevel;
        };

        // 计算每个模块的层级
        modules.forEach(module => calculateLevel(module));
        
        // 按层级分组
        const levelGroups = new Map();
        for (const [module, level] of levels) {
            if (!levelGroups.has(level)) {
                levelGroups.set(level, []);
            }
            levelGroups.get(level).push(module);
        }

        // 创建分批加载计划
        const sortedLevels = Array.from(levelGroups.keys()).sort((a, b) => a - b);
        for (const level of sortedLevels) {
            plan.push({
                level: level,
                modules: levelGroups.get(level),
                parallel: true // 同一层级的模块可以并行加载
            });
        }

        console.log(`📋 加载计划创建完成，共 ${plan.length} 个批次`);
        return plan;
    }

    /**
     * 执行分批加载
     * @SERVICE 分批加载执行方法
     * @param {Array} loadPlan - 加载计划
     * @returns {Promise<Array>} 加载结果
     */
    async executeBatchLoading(loadPlan) {
        const results = [];
        
        for (const batch of loadPlan) {
            console.log(`📦 加载批次 ${batch.level}: ${batch.modules.join(', ')}`);
            
            const batchStartTime = performance.now();
            
            if (batch.parallel) {
                // 并行加载
                const batchResults = await Promise.allSettled(
                    batch.modules.map(module => this.loadModule(module))
                );
                results.push(...batchResults);
            } else {
                // 串行加载
                for (const module of batch.modules) {
                    const result = await this.loadModule(module);
                    results.push({ status: 'fulfilled', value: result });
                }
            }
            
            const batchEndTime = performance.now();
            console.log(`✅ 批次 ${batch.level} 加载完成，耗时: ${Math.round(batchEndTime - batchStartTime)}ms`);
        }
        
        return results;
    }

    /**
     * 加载单个模块
     * @SERVICE 单个模块加载方法
     * @param {string} moduleName - 模块名称
     * @returns {Promise<Object>} 加载结果
     */
    async loadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            return { module: moduleName, status: 'cached', loadTime: 0 };
        }

        if (this.loadingModules.has(moduleName)) {
            // 等待正在加载的模块
            return await this.loadingModules.get(moduleName);
        }

        const loadPromise = this.performModuleLoad(moduleName);
        this.loadingModules.set(moduleName, loadPromise);

        try {
            const result = await loadPromise;
            this.loadedModules.add(moduleName);
            this.loadingModules.delete(moduleName);
            return result;
        } catch (error) {
            this.loadingModules.delete(moduleName);
            throw error;
        }
    }

    /**
     * 执行模块加载
     * @SERVICE 模块加载执行方法
     * @param {string} moduleName - 模块名称
     * @returns {Promise<Object>} 加载结果
     */
    async performModuleLoad(moduleName) {
        const startTime = performance.now();
        
        try {
            // 检查缓存
            if (this.moduleCache.has(moduleName)) {
                return {
                    module: moduleName,
                    status: 'cached',
                    loadTime: 0
                };
            }

            // 动态导入模块
            const moduleUrl = `./src/${moduleName}`;
            const moduleExports = await import(moduleUrl);
            
            // 缓存模块
            this.moduleCache.set(moduleName, moduleExports);
            
            const endTime = performance.now();
            const loadTime = endTime - startTime;

            console.log(`📥 模块加载成功: ${moduleName} (${Math.round(loadTime)}ms)`);

            return {
                module: moduleName,
                status: 'loaded',
                loadTime: loadTime,
                exports: moduleExports
            };

        } catch (error) {
            const endTime = performance.now();
            const loadTime = endTime - startTime;

            console.error(`❌ 模块加载失败: ${moduleName}`, error);

            return {
                module: moduleName,
                status: 'failed',
                loadTime: loadTime,
                error: error.message
            };
        }
    }

    /**
     * 更新加载统计
     * @SERVICE 加载统计更新方法
     * @param {Array} results - 加载结果
     * @param {number} totalTime - 总加载时间
     */
    updateLoadingStats(results, totalTime) {
        this.loadingStats.totalModules = results.length;
        this.loadingStats.loadedModules = results.filter(r => 
            r.status === 'fulfilled' && r.value.status === 'loaded'
        ).length;
        this.loadingStats.failedModules = results.filter(r => 
            r.status === 'rejected' || (r.value && r.value.status === 'failed')
        ).length;
        this.loadingStats.totalLoadTime = totalTime;
        this.loadingStats.averageLoadTime = totalTime / results.length;

        // 记录性能数据
        if (this.performanceMonitor) {
            this.performanceMonitor.recordMetric('moduleLoading', {
                totalModules: this.loadingStats.totalModules,
                loadedModules: this.loadingStats.loadedModules,
                failedModules: this.loadingStats.failedModules,
                totalLoadTime: this.loadingStats.totalLoadTime,
                averageLoadTime: this.loadingStats.averageLoadTime
            });
        }
    }

    /**
     * 预加载模块
     * @SERVICE 模块预加载方法
     * @param {Array} modules - 要预加载的模块
     * @returns {Promise<Array>} 预加载结果
     */
    async preloadModules(modules) {
        console.log(`🔄 开始预加载 ${modules.length} 个模块...`);
        
        const results = await Promise.allSettled(
            modules.map(module => this.loadModule(module))
        );

        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`✅ 预加载完成: ${successful} 成功, ${failed} 失败`);
        return results;
    }

    /**
     * 获取模块依赖
     * @SERVICE 模块依赖获取方法
     * @param {string} moduleName - 模块名称
     * @returns {Set} 依赖集合
     */
    getModuleDependencies(moduleName) {
        return this.dependencyGraph.get(moduleName) || new Set();
    }

    /**
     * 检查循环依赖
     * @SERVICE 循环依赖检查方法
     * @returns {Array} 循环依赖列表
     */
    checkCircularDependencies() {
        const visited = new Set();
        const visiting = new Set();
        const cycles = [];

        const visit = (module, path = []) => {
            if (visiting.has(module)) {
                const cycleStart = path.indexOf(module);
                if (cycleStart !== -1) {
                    cycles.push([...path.slice(cycleStart), module]);
                }
                return;
            }

            if (visited.has(module)) {
                return;
            }

            visiting.add(module);
            const dependencies = this.dependencyGraph.get(module) || new Set();
            
            for (const dep of dependencies) {
                visit(dep, [...path, module]);
            }

            visiting.delete(module);
            visited.add(module);
        };

        for (const module of this.dependencyGraph.keys()) {
            if (!visited.has(module)) {
                visit(module);
            }
        }

        return cycles;
    }

    /**
     * 获取加载统计
     * @SERVICE 加载统计获取方法
     * @returns {Object} 统计信息
     */
    getLoadingStats() {
        return {
            ...this.loadingStats,
            loadedModulesList: Array.from(this.loadedModules),
            cachedModulesCount: this.moduleCache.size,
            dependencyGraphSize: this.dependencyGraph.size,
            optimalLoadOrder: this.loadOrder
        };
    }

    /**
     * 清理缓存
     * @SERVICE 缓存清理方法
     */
    clearCache() {
        this.moduleCache.clear();
        this.loadedModules.clear();
        this.loadingModules.clear();
        console.log('🧹 模块缓存已清理');
    }

    /**
     * 销毁优化器
     * @LIFECYCLE 优化器销毁方法
     */
    destroy() {
        this.clearCache();
        this.dependencyGraph.clear();
        this.loadOrder = [];
        this.performanceMonitor = null;
        console.log('🗑️ ModuleLoaderOptimizer 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalModuleLoaderOptimizer = null;

/**
 * 获取全局模块加载优化器实例
 * @SERVICE 全局模块加载优化器获取函数
 * @returns {ModuleLoaderOptimizer} 模块加载优化器实例
 */
export function getModuleLoaderOptimizer() {
    if (!globalModuleLoaderOptimizer) {
        globalModuleLoaderOptimizer = new ModuleLoaderOptimizer();
    }
    return globalModuleLoaderOptimizer;
}

// ==================== 便捷函数 ====================

/**
 * 优化模块加载
 * @SERVICE 模块加载优化函数
 * @param {Array} modules - 模块列表
 * @returns {Promise<Object>} 优化结果
 */
export async function optimizeModuleLoading(modules = []) {
    return await getModuleLoaderOptimizer().optimizeModuleLoading(modules);
}

/**
 * 预加载关键模块
 * @SERVICE 关键模块预加载函数
 * @returns {Promise<Array>} 预加载结果
 */
export async function preloadCriticalModules() {
    const criticalModules = [
        'utils.js',
        'constants.js',
        'service-container.js',
        'event-bus.js',
        'notification.js',
        'modal.js'
    ];
    
    return await getModuleLoaderOptimizer().preloadModules(criticalModules);
}

/**
 * 检查系统依赖健康状况
 * @SERVICE 系统依赖健康检查函数
 * @returns {Object} 健康状况报告
 */
export function checkDependencyHealth() {
    const optimizer = getModuleLoaderOptimizer();
    const cycles = optimizer.checkCircularDependencies();
    const stats = optimizer.getLoadingStats();
    
    return {
        hasCircularDependencies: cycles.length > 0,
        circularDependencies: cycles,
        loadingStats: stats,
        healthScore: cycles.length === 0 ? 100 : Math.max(0, 100 - cycles.length * 20)
    };
}
